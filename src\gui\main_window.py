"""
Main GUI window for the file transfer application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from pathlib import Path
from typing import Optional, List
from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.core.resilient_client import ResilientFileTransferClient
from src.core.resilient_server import ResilientFileTransferServer
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.logger import get_logger


class MainWindow:
    """
    Main application window with tabbed interface for server and client modes.
    """
    
    def __init__(self):
        """Initialize the main window."""
        self.logger = get_logger()
        
        # Create main window with improved sizing
        self.root = tk.Tk()
        self.root.title("File Transfer Application v2.0.0")
        self.root.geometry("1000x700")  # Larger default size for better layout
        self.root.minsize(800, 600)     # Larger minimum size to accommodate content
        
        # Application state
        self.server: Optional[FileTransferServer] = None
        self.client: Optional[FileTransferClient] = None
        self.resilient_client: Optional[ResilientFileTransferClient] = None
        self.resilient_server: Optional[ResilientFileTransferServer] = None
        self.server_thread: Optional[threading.Thread] = None

        # Transfer control state
        self.transfer_active: bool = False
        self.transfer_paused: bool = False
        self.transfer_thread: Optional[threading.Thread] = None
        self.current_transfer_id: Optional[str] = None

        # Transfer settings
        self.use_resilient_transfers = tk.BooleanVar(value=True)
        
        # Setup GUI
        self._setup_styles()
        self._create_widgets()
        self._setup_callbacks()
        
        # Center window
        self._center_window()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _setup_styles(self):
        """Setup custom styles for the application."""
        # Apply modern theme instead of custom styles
        from src.gui.theme import ModernTheme
        self.style = ModernTheme.apply_theme(self.root)
        
    def _create_widgets(self):
        """Create and layout GUI widgets with improved sizing and spacing."""
        # Main container with better padding
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights for better responsiveness
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Title with improved styling
        title_label = ttk.Label(main_frame, text="File Transfer Application",
                               style='Heading.TLabel', font=('Segoe UI', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 25))

        # Create notebook for tabs with better styling
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self._create_send_tab()
        self._create_receive_tab()
        self._create_server_tab()
        self._create_settings_tab()
        
        # Status bar
        self._create_status_bar(main_frame)
    
    def _create_send_tab(self):
        """Create the send files tab with improved layout and sizing."""
        send_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(send_frame, text="Send Files")

        # Configure grid with better proportions
        send_frame.columnconfigure(0, weight=1)
        send_frame.rowconfigure(1, weight=1)

        # Connection settings with improved spacing and sizing
        conn_frame = ttk.LabelFrame(send_frame, text="Connection Settings", padding="15")
        conn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        conn_frame.columnconfigure(1, weight=3)  # Host field gets more space
        conn_frame.columnconfigure(3, weight=1)  # Port field smaller but adequate

        # Host input with better sizing and font
        ttk.Label(conn_frame, text="Host:", font=('Segoe UI', 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.host_var = tk.StringVar(value="localhost")
        host_entry = ttk.Entry(conn_frame, textvariable=self.host_var, font=('Segoe UI', 10))
        host_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        # Port input with appropriate sizing
        ttk.Label(conn_frame, text="Port:", font=('Segoe UI', 10)).grid(row=0, column=2, sticky=tk.W, padx=(0, 8))
        self.port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(conn_frame, textvariable=self.port_var, font=('Segoe UI', 10), width=8)
        port_entry.grid(row=0, column=3, sticky=tk.W, padx=(0, 15))

        # Connect button with better styling and sizing
        self.connect_btn = ttk.Button(conn_frame, text="Connect", command=self._connect_to_server,
                                     style='ConnectAction.TButton', width=12)
        self.connect_btn.grid(row=0, column=4, padx=(5, 0))

        # Connection status with better spacing and font
        self.connection_status = ttk.Label(conn_frame, text="Not connected", style='Status.TLabel',
                                         font=('Segoe UI', 9))
        self.connection_status.grid(row=1, column=0, columnspan=5, sticky=tk.W, pady=(12, 0))

        # Transfer options with improved layout
        options_frame = ttk.LabelFrame(send_frame, text="Transfer Options", padding="15")
        options_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        options_frame.columnconfigure(1, weight=1)

        # Resilient transfer option
        ttk.Checkbutton(options_frame, text="Enable resilient transfers (auto-resume on interruption)",
                       variable=self.use_resilient_transfers).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Transfer status display
        self.transfer_status_label = ttk.Label(options_frame, text="Ready", font=('Segoe UI', 9))
        self.transfer_status_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Progress bar for resilient transfers
        self.resilient_progress = ttk.Progressbar(options_frame, mode='determinate', length=300)
        self.resilient_progress.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        self.resilient_progress.grid_remove()  # Hide initially

        # Speed display
        self.speed_label = ttk.Label(options_frame, text="", font=('Segoe UI', 9))
        self.speed_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(2, 0))

        # File selection with improved layout and spacing
        file_frame = ttk.LabelFrame(send_frame, text="File Selection", padding="15")
        file_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)

        # File selection buttons with better spacing and sizing
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 12))

        # Improved button layout with consistent sizing
        ttk.Button(btn_frame, text="Add Files", command=self._add_files,
                  style='PrimaryAction.TButton', width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(btn_frame, text="Add Folder", command=self._add_folder,
                  width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(btn_frame, text="Clear List", command=self._clear_files,
                  width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(btn_frame, text="Resume Transfers", command=self._show_resume_dialog,
                  width=15).pack(side=tk.LEFT, padx=(0, 8))

        # File list with improved layout
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Create treeview for file list with better column sizing
        self.file_tree = ttk.Treeview(list_frame, columns=('size', 'status'), show='tree headings', height=8)
        self.file_tree.heading('#0', text='File Name')
        self.file_tree.heading('size', text='Size')
        self.file_tree.heading('status', text='Status')

        # Better proportional column widths
        self.file_tree.column('#0', width=400, minwidth=200)
        self.file_tree.column('size', width=120, minwidth=80)
        self.file_tree.column('status', width=120, minwidth=80)

        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))

        # Scrollbar for file list with better positioning
        file_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)

        # Transfer controls with improved layout and spacing
        transfer_frame = ttk.Frame(send_frame, padding="10")
        transfer_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        transfer_frame.columnconfigure(2, weight=1)

        # Transfer control buttons frame
        control_buttons_frame = ttk.Frame(transfer_frame)
        control_buttons_frame.grid(row=0, column=0, padx=(0, 15))

        # Send button with better sizing
        self.send_btn = ttk.Button(control_buttons_frame, text="Send Files", command=self._send_files,
                                  state='disabled', style='SuccessAction.TButton', width=12)
        self.send_btn.grid(row=0, column=0, padx=(0, 5))

        # Stop button
        self.stop_btn = ttk.Button(control_buttons_frame, text="Stop", command=self._stop_transfer,
                                  state='disabled', style='DangerAction.TButton', width=8)
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))

        # Pause button
        self.pause_btn = ttk.Button(control_buttons_frame, text="Pause", command=self._pause_transfer,
                                   state='disabled', width=8)
        self.pause_btn.grid(row=0, column=2, padx=(0, 5))

        # Resume button
        self.resume_btn = ttk.Button(control_buttons_frame, text="Resume", command=self._resume_transfer,
                                    state='disabled', style='SuccessAction.TButton', width=8)
        self.resume_btn.grid(row=0, column=3, padx=(0, 5))

        # Cancel button
        self.cancel_btn = ttk.Button(control_buttons_frame, text="Cancel", command=self._cancel_transfer,
                                    state='disabled', style='DangerAction.TButton', width=8)
        self.cancel_btn.grid(row=0, column=4)

        # Speed display frame
        speed_frame = ttk.Frame(transfer_frame)
        speed_frame.grid(row=0, column=1, padx=(0, 15))

        self.speed_label = ttk.Label(speed_frame, text="Speed: --", font=('Segoe UI', 9))
        self.speed_label.grid(row=0, column=0)

        # Progress bar with better proportions
        self.send_progress = ttk.Progressbar(transfer_frame, mode='determinate')
        self.send_progress.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=(0, 15))

        # Progress label with better font
        self.send_progress_label = ttk.Label(transfer_frame, text="Ready", font=('Segoe UI', 9))
        self.send_progress_label.grid(row=0, column=3)
        
        # Store selected files
        self.selected_files: List[str] = []



    def _create_receive_tab(self):
        """Create the receive files tab focused on incoming file monitoring."""
        receive_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(receive_frame, text="Receive Files")

        # Configure grid with better proportions
        receive_frame.columnconfigure(0, weight=1)
        receive_frame.rowconfigure(1, weight=1)

        # Server status section
        status_frame = ttk.LabelFrame(receive_frame, text="Server Status", padding="15")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        status_frame.columnconfigure(1, weight=1)

        # Server status indicator
        status_info_frame = ttk.Frame(status_frame)
        status_info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))
        status_info_frame.columnconfigure(1, weight=1)

        self.server_status_indicator = ttk.Label(status_info_frame, text="●", foreground="red", font=('Segoe UI', 14))
        self.server_status_indicator.grid(row=0, column=0, padx=(0, 8))

        self.server_status_text = ttk.Label(status_info_frame, text="Server: Stopped", style='Status.TLabel',
                                           font=('Segoe UI', 11, 'bold'))
        self.server_status_text.grid(row=0, column=1, sticky=tk.W)

        self.clients_info_label = ttk.Label(status_info_frame, text="Clients: 0", style='Status.TLabel',
                                           font=('Segoe UI', 10))
        self.clients_info_label.grid(row=0, column=2, sticky=tk.E)

        # Quick server control
        quick_control_frame = ttk.Frame(status_frame)
        quick_control_frame.grid(row=1, column=0, columnspan=2, pady=(15, 0))

        ttk.Label(quick_control_frame, text="Quick Actions:", font=('Segoe UI', 10)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(quick_control_frame, text="Go to Server Tab",
                  command=lambda: self.notebook.select(2),
                  style='Secondary.TButton', width=15).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(quick_control_frame, text="Open Download Folder",
                  command=self._open_download_folder,
                  style='Secondary.TButton', width=18).pack(side=tk.LEFT)

        # Activity log for incoming files
        log_frame = ttk.LabelFrame(receive_frame, text="Incoming Files Activity", padding="15")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Create text widget for log with improved sizing
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_text_frame.columnconfigure(0, weight=1)
        log_text_frame.rowconfigure(0, weight=1)

        # Improved log text widget with better font and sizing
        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, state='disabled', height=15,
                               font=('Consolas', 9), bg='#f8f9fa', relief='flat', borderwidth=1)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))

        # Scrollbar for log with better positioning
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # Log control buttons
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, pady=(10, 0))

        ttk.Button(log_btn_frame, text="Clear Log", command=self._clear_activity_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_btn_frame, text="Save Log", command=self._save_activity_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_btn_frame, text="Refresh", command=self._refresh_activity_log).pack(side=tk.LEFT)

    def _create_server_tab(self):
        """Create the dedicated server tab with full server management."""
        server_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(server_frame, text="Server")

        # Configure grid with better proportions
        server_frame.columnconfigure(0, weight=1)
        server_frame.rowconfigure(2, weight=1)

        # Server configuration section
        config_frame = ttk.LabelFrame(server_frame, text="Server Configuration", padding="15")
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(1, weight=3)  # Main fields get more space
        config_frame.columnconfigure(3, weight=1)  # Port field smaller

        # Listen address with better sizing and font
        ttk.Label(config_frame, text="Listen on:", font=('Segoe UI', 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.listen_host_var = tk.StringVar(value="0.0.0.0")
        host_combo = ttk.Combobox(config_frame, textvariable=self.listen_host_var, state='readonly',
                                 font=('Segoe UI', 10))
        host_combo['values'] = ['0.0.0.0 (All interfaces)', 'localhost (Local only)'] + NetworkUtils.get_all_local_ips()
        host_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        # Port with appropriate sizing
        ttk.Label(config_frame, text="Port:", font=('Segoe UI', 10)).grid(row=0, column=2, sticky=tk.W, padx=(0, 8))
        self.listen_port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(config_frame, textvariable=self.listen_port_var, font=('Segoe UI', 10), width=8)
        port_entry.grid(row=0, column=3, sticky=tk.W)

        # Download directory with better layout
        ttk.Label(config_frame, text="Download to:", font=('Segoe UI', 10)).grid(row=1, column=0, sticky=tk.W, padx=(0, 8), pady=(12, 0))
        self.download_dir_var = tk.StringVar(value=str(Path.home() / "Downloads" / "FileTransfer"))
        dir_entry = ttk.Entry(config_frame, textvariable=self.download_dir_var, font=('Segoe UI', 10))
        dir_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 8), pady=(12, 0))

        ttk.Button(config_frame, text="Browse", command=self._browse_download_dir, width=10).grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=(8, 0), pady=(12, 0))

        # Server controls section
        control_frame = ttk.LabelFrame(server_frame, text="Server Control", padding="15")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        control_frame.columnconfigure(2, weight=1)

        # Server control buttons with improved spacing and sizing
        self.start_server_btn = ttk.Button(control_frame, text="Start Server", command=self._start_server,
                                          style='SuccessAction.TButton', width=15)
        self.start_server_btn.grid(row=0, column=0, padx=(0, 12))

        self.stop_server_btn = ttk.Button(control_frame, text="Stop Server", command=self._stop_server,
                                         state='disabled', style='DangerAction.TButton', width=15)
        self.stop_server_btn.grid(row=0, column=1, padx=(0, 15))

        # Server status with better styling
        status_display_frame = ttk.Frame(control_frame)
        status_display_frame.grid(row=0, column=2, sticky=(tk.W, tk.E))
        status_display_frame.columnconfigure(1, weight=1)

        self.server_status_indicator = ttk.Label(status_display_frame, text="●", foreground="red", font=('Segoe UI', 14))
        self.server_status_indicator.grid(row=0, column=0, padx=(0, 8))

        self.server_status_text = ttk.Label(status_display_frame, text="Server: Stopped", style='Status.TLabel',
                                           font=('Segoe UI', 11, 'bold'))
        self.server_status_text.grid(row=0, column=1, sticky=tk.W)

        self.clients_info_label = ttk.Label(status_display_frame, text="Clients: 0", style='Status.TLabel',
                                           font=('Segoe UI', 10))
        self.clients_info_label.grid(row=0, column=2, sticky=tk.E)

        # Server information section
        info_frame = ttk.Frame(control_frame)
        info_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(15, 0))
        info_frame.columnconfigure(1, weight=1)

        local_ip = NetworkUtils.get_local_ip()
        ttk.Label(info_frame, text="Local IP:", font=('Segoe UI', 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.local_ip_label = ttk.Label(info_frame, text=local_ip, style='Status.TLabel', font=('Segoe UI', 9))
        self.local_ip_label.grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Server Address:", font=('Segoe UI', 9)).grid(row=1, column=0, sticky=tk.W, padx=(0, 8), pady=(5, 0))
        self.server_address_label = ttk.Label(info_frame, text="Not running", style='Status.TLabel', font=('Segoe UI', 9))
        self.server_address_label.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        ttk.Label(info_frame, text="Connected Clients:", font=('Segoe UI', 9)).grid(row=2, column=0, sticky=tk.W, padx=(0, 8), pady=(5, 0))
        self.connected_clients_label = ttk.Label(info_frame, text="0", style='Status.TLabel', font=('Segoe UI', 9))
        self.connected_clients_label.grid(row=2, column=1, sticky=tk.W, pady=(5, 0))

        # Server activity monitoring
        activity_frame = ttk.LabelFrame(server_frame, text="Server Activity & Client Monitoring", padding="15")
        activity_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        activity_frame.columnconfigure(0, weight=1)
        activity_frame.rowconfigure(0, weight=1)

        # Create text widget for server log
        server_log_frame = ttk.Frame(activity_frame)
        server_log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        server_log_frame.columnconfigure(0, weight=1)
        server_log_frame.rowconfigure(0, weight=1)

        # Server log text widget
        self.server_log_text = tk.Text(server_log_frame, wrap=tk.WORD, state='disabled', height=12,
                                      font=('Consolas', 9), bg='#f8f9fa', relief='flat', borderwidth=1)
        self.server_log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 2))

        # Scrollbar for server log
        server_log_scrollbar = ttk.Scrollbar(server_log_frame, orient=tk.VERTICAL, command=self.server_log_text.yview)
        server_log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.server_log_text.configure(yscrollcommand=server_log_scrollbar.set)

        # Server log control buttons
        server_log_btn_frame = ttk.Frame(activity_frame)
        server_log_btn_frame.grid(row=1, column=0, pady=(10, 0))

        ttk.Button(server_log_btn_frame, text="Clear Server Log", command=self._clear_server_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(server_log_btn_frame, text="Save Server Log", command=self._save_server_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(server_log_btn_frame, text="Export Statistics", command=self._export_server_stats).pack(side=tk.LEFT)

    def _create_settings_tab(self):
        """Create the enhanced settings tab with improved layout and sizing."""
        settings_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(settings_frame, text="Settings")

        # Configure grid for scrollable content with better proportions
        settings_frame.columnconfigure(0, weight=1)
        settings_frame.rowconfigure(0, weight=1)

        # Create scrollable frame with improved styling
        canvas = tk.Canvas(settings_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Configure scrollable frame
        scrollable_frame.columnconfigure(0, weight=1)

        # Initialize font size variable
        self.font_size_var = tk.StringVar(value="9")

        # Network & Transfer Settings Section
        network_frame = ttk.LabelFrame(scrollable_frame, text="🌐 Network & Transfer Settings", padding="20")
        network_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        network_frame.columnconfigure(1, weight=1)
        network_frame.columnconfigure(3, weight=1)
        network_frame.columnconfigure(5, weight=1)

        # First row - Chunk size and Connection timeout
        ttk.Label(network_frame, text="Transfer Chunk Size (KB):", font=('Segoe UI', 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.chunk_size_var = tk.StringVar(value="8")
        chunk_entry = ttk.Entry(network_frame, textvariable=self.chunk_size_var, font=('Segoe UI', 10), width=12)
        chunk_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        ttk.Label(network_frame, text="Connection Timeout (s):", font=('Segoe UI', 10)).grid(row=0, column=2, sticky=tk.W, padx=(0, 8))
        self.timeout_var = tk.StringVar(value="10")
        timeout_entry = ttk.Entry(network_frame, textvariable=self.timeout_var, font=('Segoe UI', 10), width=12)
        timeout_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(0, 20))

        ttk.Label(network_frame, text="Max Concurrent Connections:", font=('Segoe UI', 10)).grid(row=0, column=4, sticky=tk.W, padx=(0, 8))
        self.max_connections_var = tk.StringVar(value="5")
        max_conn_entry = ttk.Entry(network_frame, textvariable=self.max_connections_var, font=('Segoe UI', 10), width=12)
        max_conn_entry.grid(row=0, column=5, sticky=(tk.W, tk.E))

        # Second row - Help text
        ttk.Label(network_frame, text="(1-64 KB recommended)", font=('Segoe UI', 8), foreground='gray').grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        ttk.Label(network_frame, text="(5-60 seconds)", font=('Segoe UI', 8), foreground='gray').grid(row=1, column=3, sticky=tk.W, pady=(5, 0))
        ttk.Label(network_frame, text="(1-20 connections)", font=('Segoe UI', 8), foreground='gray').grid(row=1, column=5, sticky=tk.W, pady=(5, 0))

        # User Interface Settings Section
        ui_frame = ttk.LabelFrame(scrollable_frame, text="🎨 User Interface Settings", padding="20")
        ui_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        ui_frame.columnconfigure(1, weight=1)
        ui_frame.columnconfigure(3, weight=1)

        # First row - Font size and Theme
        ttk.Label(ui_frame, text="Font Size:", font=('Segoe UI', 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        font_frame = ttk.Frame(ui_frame)
        font_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 40))
        font_frame.columnconfigure(0, weight=1)

        font_sizes = ["8", "9", "10", "11", "12", "14", "16", "18", "20"]
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_size_var, values=font_sizes,
                                 width=12, state="readonly", font=('Segoe UI', 10))
        font_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))

        ttk.Button(font_frame, text="Apply", command=self._apply_font_size, width=10).grid(row=0, column=1)

        # Theme selection
        ttk.Label(ui_frame, text="Theme:", font=('Segoe UI', 10)).grid(row=0, column=2, sticky=tk.W, padx=(0, 8))
        self.theme_var = tk.StringVar(value="Modern")
        theme_combo = ttk.Combobox(ui_frame, textvariable=self.theme_var, values=["Modern", "Classic", "Dark"],
                                  width=15, state="readonly", font=('Segoe UI', 10))
        theme_combo.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # UI preferences - arranged in columns
        ui_prefs_frame = ttk.Frame(ui_frame)
        ui_prefs_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(20, 0))
        ui_prefs_frame.columnconfigure(0, weight=1)
        ui_prefs_frame.columnconfigure(1, weight=1)
        ui_prefs_frame.columnconfigure(2, weight=1)

        self.show_notifications_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(ui_prefs_frame, text="Show transfer completion notifications",
                       variable=self.show_notifications_var).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        self.auto_clear_log_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(ui_prefs_frame, text="Auto-clear completed transfers from log",
                       variable=self.auto_clear_log_var).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        self.remember_window_size_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(ui_prefs_frame, text="Remember window size and position",
                       variable=self.remember_window_size_var).grid(row=0, column=2, sticky=tk.W)

        # File Transfer Preferences Section
        transfer_frame = ttk.LabelFrame(scrollable_frame, text="📁 File Transfer Preferences", padding="20")
        transfer_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        transfer_frame.columnconfigure(1, weight=1)

        # Default download directory - full width
        ttk.Label(transfer_frame, text="Default Download Directory:", font=('Segoe UI', 10)).grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.default_download_dir_var = tk.StringVar(value=str(Path.home() / "Downloads" / "FileTransfer"))
        download_dir_frame = ttk.Frame(transfer_frame)
        download_dir_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 15))
        download_dir_frame.columnconfigure(0, weight=1)

        download_dir_entry = ttk.Entry(download_dir_frame, textvariable=self.default_download_dir_var, font=('Segoe UI', 10))
        download_dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(download_dir_frame, text="Browse", command=self._browse_default_download_dir, width=12).grid(row=0, column=1)

        # Transfer preferences - arranged in columns for better space usage
        transfer_prefs_frame = ttk.Frame(transfer_frame)
        transfer_prefs_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        transfer_prefs_frame.columnconfigure(0, weight=1)
        transfer_prefs_frame.columnconfigure(1, weight=1)
        transfer_prefs_frame.columnconfigure(2, weight=1)

        self.overwrite_files_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(transfer_prefs_frame, text="Overwrite existing files without asking",
                       variable=self.overwrite_files_var).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        self.preserve_timestamps_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(transfer_prefs_frame, text="Preserve file timestamps",
                       variable=self.preserve_timestamps_var).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        self.create_subdirs_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(transfer_prefs_frame, text="Create subdirectories for organized transfers",
                       variable=self.create_subdirs_var).grid(row=0, column=2, sticky=tk.W)

        # Resilient transfer preferences
        resilient_prefs_frame = ttk.Frame(transfer_frame)
        resilient_prefs_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
        resilient_prefs_frame.columnconfigure(0, weight=1)
        resilient_prefs_frame.columnconfigure(1, weight=1)

        ttk.Label(resilient_prefs_frame, text="Resilient Transfer Options:", font=('Segoe UI', 10, 'bold')).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.enable_resilient_default_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(resilient_prefs_frame, text="Enable resilient transfers by default",
                       variable=self.enable_resilient_default_var).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))

        self.auto_resume_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(resilient_prefs_frame, text="Auto-resume transfers on reconnection",
                       variable=self.auto_resume_var).grid(row=1, column=1, sticky=tk.W)

        # Security & Compression Settings Section
        security_frame = ttk.LabelFrame(scrollable_frame, text="🔒 Security & Compression", padding="20")
        security_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        security_frame.columnconfigure(0, weight=1)
        security_frame.columnconfigure(1, weight=1)

        # Security options - left column
        security_options_frame = ttk.Frame(security_frame)
        security_options_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 20))

        ttk.Label(security_options_frame, text="Security Options:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        self.encryption_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(security_options_frame, text="Enable file encryption (experimental)",
                       variable=self.encryption_var).pack(anchor=tk.W, pady=(0, 8))

        self.checksum_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(security_options_frame, text="Verify file checksums for integrity",
                       variable=self.checksum_var).pack(anchor=tk.W)

        # Compression options - right column
        compression_frame = ttk.Frame(security_frame)
        compression_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N))

        ttk.Label(compression_frame, text="Compression Options:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        self.enable_compression_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(compression_frame, text="Enable file compression",
                       variable=self.enable_compression_var).pack(anchor=tk.W, pady=(0, 8))

        compression_level_frame = ttk.Frame(compression_frame)
        compression_level_frame.pack(anchor=tk.W, fill=tk.X)

        ttk.Label(compression_level_frame, text="Compression level:", font=('Segoe UI', 9)).pack(anchor=tk.W, pady=(0, 5))
        self.compression_level_var = tk.StringVar(value="6")
        compression_combo = ttk.Combobox(compression_level_frame, textvariable=self.compression_level_var,
                                        values=["1 (Fast)", "3", "6 (Balanced)", "9 (Best)"],
                                        width=15, state="readonly", font=('Segoe UI', 9))
        compression_combo.pack(anchor=tk.W)

        # Advanced Settings Section
        advanced_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Advanced Settings", padding="20")
        advanced_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        advanced_frame.columnconfigure(0, weight=1)
        advanced_frame.columnconfigure(1, weight=1)

        # Logging settings - left column
        logging_frame = ttk.Frame(advanced_frame)
        logging_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 20))

        ttk.Label(logging_frame, text="Logging Options:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        self.enable_logging_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(logging_frame, text="Enable detailed logging",
                       variable=self.enable_logging_var).pack(anchor=tk.W, pady=(0, 8))

        self.auto_save_logs_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(logging_frame, text="Auto-save logs to file",
                       variable=self.auto_save_logs_var).pack(anchor=tk.W)

        # Performance settings - right column
        performance_frame = ttk.Frame(advanced_frame)
        performance_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N))

        ttk.Label(performance_frame, text="Performance Options:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        self.enable_statistics_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(performance_frame, text="Enable transfer statistics",
                       variable=self.enable_statistics_var).pack(anchor=tk.W, pady=(0, 8))

        self.auto_optimize_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(performance_frame, text="Auto-optimize transfer settings",
                       variable=self.auto_optimize_var).pack(anchor=tk.W)

        # Application Info Section
        info_frame = ttk.LabelFrame(scrollable_frame, text="ℹ️ Application Information", padding="15")
        info_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        info_text = """File Transfer Application v2.0.0

A modern, secure, and user-friendly file transfer application with built-in server capabilities.

✨ Key Features:
• TCP-based reliable file transfer with progress tracking
• Dedicated server mode with client monitoring
• File compression and integrity verification
• Modern tabbed interface with improved UX
• Real-time activity logging and statistics
• Cross-platform compatibility

🔧 Technical Details:
• Python-based with Tkinter GUI
• Socket-based networking with chunked transfers
• Configurable compression and security options
• Extensible architecture for future enhancements

👨‍💻 Developed by: Hamza Damra
📧 Email: <EMAIL>
🏫 Al-Quds University"""

        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT, font=('Segoe UI', 9))
        info_label.grid(row=0, column=0, sticky=(tk.W, tk.N))

        # Settings control buttons - centered and well-spaced
        settings_btn_frame = ttk.Frame(scrollable_frame)
        settings_btn_frame.grid(row=6, column=0, pady=(25, 10), sticky=(tk.W, tk.E))
        settings_btn_frame.columnconfigure(0, weight=1)
        settings_btn_frame.columnconfigure(1, weight=1)
        settings_btn_frame.columnconfigure(2, weight=1)

        ttk.Button(settings_btn_frame, text="Reset to Defaults",
                  command=self._reset_settings_to_defaults,
                  style='Secondary.TButton', width=20).grid(row=0, column=0, padx=10)

        ttk.Button(settings_btn_frame, text="Export Settings",
                  command=self._export_settings,
                  style='Secondary.TButton', width=20).grid(row=0, column=1, padx=10)

        ttk.Button(settings_btn_frame, text="Import Settings",
                  command=self._import_settings,
                  style='Secondary.TButton', width=20).grid(row=0, column=2, padx=10)

        # Configure scrollable frame
        scrollable_frame.columnconfigure(0, weight=1)

    def _create_status_bar(self, parent):
        """Create the status bar."""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)

        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready", style='Status.TLabel')
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Network info
        local_ip = NetworkUtils.get_local_ip()
        network_info = f"Local IP: {local_ip}"
        self.network_label = ttk.Label(status_frame, text=network_info, style='Status.TLabel')
        self.network_label.grid(row=0, column=1, sticky=tk.E)

    def _setup_callbacks(self):
        """Setup callbacks for server and client events."""
        # This will be implemented when we create the server and client instances
        pass

    def _center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _on_closing(self):
        """Handle window closing event."""
        if self.server:
            self._stop_server()
        if self.client and self.client.is_connected():
            self.client.disconnect()
        self.root.destroy()

    def _apply_font_size(self):
        """Apply the selected font size to the application."""
        try:
            font_size = int(self.font_size_var.get())

            # Update the theme with new font size
            from src.gui.theme import ModernTheme
            ModernTheme.FONTS['default'] = ('Segoe UI', font_size)
            ModernTheme.FONTS['heading'] = ('Segoe UI', font_size + 2, 'bold')
            ModernTheme.FONTS['subheading'] = ('Segoe UI', font_size + 1, 'bold')

            # Reapply theme
            self.style = ModernTheme.apply_theme(self.root)

            # Show confirmation
            messagebox.showinfo("Font Size", f"Font size updated to {font_size}pt. Some changes may require restarting the application.")

        except ValueError:
            messagebox.showerror("Error", "Invalid font size selected")



    # Server management methods (for receive tab)
    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select download directory",
            initialdir=self.download_dir_var.get()
        )
        if directory:
            self.download_dir_var.set(directory)

    def _start_server(self):
        """Start the file transfer server from receive tab."""
        try:
            host = self.listen_host_var.get().split()[0]  # Extract IP from combo text
            port = int(self.listen_port_var.get().strip())
            download_dir = self.download_dir_var.get().strip()

            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Error", "Invalid port number (1-65535)")
                return

            # Check if port is available
            if not NetworkUtils.is_port_available(host if host != "0.0.0.0" else "localhost", port):
                messagebox.showerror("Error", f"Port {port} is already in use")
                return

            # Create download directory
            if not FileUtils.ensure_directory_exists(download_dir):
                messagebox.showerror("Error", f"Cannot create download directory: {download_dir}")
                return

            # Create server and setup callbacks
            chunk_size = int(self.chunk_size_var.get()) * 1024  # Convert KB to bytes
            self.server = FileTransferServer(
                host=host,
                port=port,
                download_dir=download_dir,
                chunk_size=chunk_size
            )
            self._setup_server_callbacks()

            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.server.start, daemon=True)
            self.server_thread.start()

            # Update UI
            self.start_server_btn.config(state='disabled')
            self.stop_server_btn.config(state='normal')
            self._update_server_status_ui(True, port)
            self._log_message(f"Server started on {host}:{port}")
            self._log_message(f"Download directory: {download_dir}")

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
        except Exception as e:
            messagebox.showerror("Server Error", f"Error starting server: {e}")

    def _stop_server(self):
        """Stop the file transfer server from receive tab."""
        if self.server:
            self.server.stop()
            self.server = None
            self.server_thread = None

            # Update UI
            self.start_server_btn.config(state='normal')
            self.stop_server_btn.config(state='disabled')
            self._update_server_status_ui(False)
            self._log_message("Server stopped")

    def _setup_server_callbacks(self):
        """Setup callbacks for server events."""
        if self.server:
            self.server.on_client_connected = self._on_server_client_connected
            self.server.on_client_disconnected = self._on_server_client_disconnected
            self.server.on_file_received = self._on_server_file_received
            self.server.on_transfer_progress = self._on_server_transfer_progress_callback

    def _clear_server_log(self):
        """Clear the server activity log."""
        self.server_log.delete(1.0, tk.END)

    def _log_server_message(self, message: str):
        """Log a message to the server activity log."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.server_log.insert(tk.END, log_entry)
        self.server_log.see(tk.END)

        # Limit log size (keep last 1000 lines)
        lines = self.server_log.get(1.0, tk.END).split('\n')
        if len(lines) > 1000:
            self.server_log.delete(1.0, f"{len(lines) - 1000}.0")

    # Server event callbacks
    def _on_server_client_connected(self, client_id: str, client_address: tuple):
        """Handle client connection to server."""
        message = f"Client connected: {client_id} from {client_address[0]}:{client_address[1]}"
        self.root.after(0, lambda: self._log_server_message(message))

        # Update connected clients count
        if self.server:
            count = len(self.server.active_connections) if hasattr(self.server, 'active_connections') else 1
            self.root.after(0, lambda: self.connected_clients_label.config(text=str(count)))
            self.root.after(0, lambda: self.clients_info_label.config(text=f"Clients: {count}"))

    def _on_server_client_disconnected(self, client_id: str):
        """Handle client disconnection from server."""
        message = f"Client disconnected: {client_id}"
        self.root.after(0, lambda: self._log_server_message(message))

        # Update connected clients count
        if self.server:
            count = len(self.server.active_connections) if hasattr(self.server, 'active_connections') else 0
            self.root.after(0, lambda: self.connected_clients_label.config(text=str(count)))
            self.root.after(0, lambda: self.clients_info_label.config(text=f"Clients: {count}"))

    def _on_server_file_received(self, filename: str, client_id: str):
        """Handle file received by server."""
        message = f"File received: {filename} from {client_id}"
        self.root.after(0, lambda: self._log_server_message(message))

    def _on_server_transfer_progress_callback(self, filename: str, progress: float, bytes_received: int, total_bytes: int, client_id: str):
        """Handle server transfer progress."""
        if progress >= 100.0:
            message = f"Transfer completed: {filename} ({FileUtils.format_file_size(total_bytes)})"
        else:
            message = f"Receiving {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)})"
        self.root.after(0, lambda: self._log_server_message(message))

    # File selection methods
    def _add_files(self):
        """Add files to the transfer list."""
        files = filedialog.askopenfilenames(
            title="Select files to transfer",
            filetypes=[("All files", "*.*")]
        )

        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                self._add_file_to_tree(file_path)

        # Update button states
        if not self.transfer_active:
            self._update_transfer_buttons_state('idle')

    def _add_folder(self):
        """Add all files from a folder to the transfer list."""
        folder_path = filedialog.askdirectory(title="Select folder to transfer")

        if folder_path:
            folder = Path(folder_path)
            for file_path in folder.rglob('*'):
                if file_path.is_file():
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
                        self._add_file_to_tree(file_str)

            # Update button states
            if not self.transfer_active:
                self._update_transfer_buttons_state('idle')

    def _clear_files(self):
        """Clear the file list."""
        self.selected_files.clear()
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # Update button states
        if not self.transfer_active:
            self._update_transfer_buttons_state('idle')

    def _add_file_to_tree(self, file_path: str):
        """Add a file to the tree view."""
        file_info = FileUtils.get_file_info(file_path)
        if file_info:
            self.file_tree.insert('', 'end',
                                text=file_info['name'],
                                values=(file_info['size_human'], 'Ready'))

    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select download directory",
            initialdir=self.download_dir_var.get()
        )

        if directory:
            self.download_dir_var.set(directory)

    # Connection methods
    def _connect_to_server(self):
        """Connect to the file transfer server."""
        if self.client and self.client.is_connected():
            self.client.disconnect()
            self.connect_btn.config(text="Connect")
            self.connection_status.config(text="Not connected")
            self._update_transfer_buttons_state('idle')
            return

        try:
            host = self.host_var.get().strip()
            port = int(self.port_var.get().strip())

            # Validate host - allow IP addresses, localhost, and hostnames
            if not host:
                messagebox.showerror("Error", "Please enter a host address")
                return

            # Use the Validator class for proper host validation
            from src.utils.error_handler import Validator
            if not Validator.validate_host(host):
                messagebox.showerror("Error", "Invalid host address. Please enter a valid IP address, hostname, or 'localhost'")
                return

            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Error", "Invalid port number (1-65535)")
                return

            # Create client and setup callbacks
            chunk_size = int(self.chunk_size_var.get()) * 1024  # Convert KB to bytes
            self.client = FileTransferClient(chunk_size=chunk_size)
            self._setup_client_callbacks()

            # Connect
            self.connection_status.config(text="Connecting...")
            self.root.update()

            if self.client.connect(host, port):
                self.connect_btn.config(text="Disconnect")
                self.connection_status.config(text=f"Connected to {host}:{port}")
                self._update_transfer_buttons_state('idle')
                self._log_message(f"Connected to server {host}:{port}")
            else:
                self.connection_status.config(text="Connection failed")
                error_msg = f"Failed to connect to {host}:{port}\n\nPossible causes:\n"
                error_msg += "• Server is not running\n"
                error_msg += "• Incorrect host or port\n"
                error_msg += "• Firewall blocking connection\n"
                error_msg += "• Network connectivity issues"
                messagebox.showerror("Connection Error", error_msg)

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
        except Exception as e:
            self.connection_status.config(text="Connection failed")
            messagebox.showerror("Connection Error", f"Error: {e}")



    def _send_files(self):
        """Send selected files to the server."""
        if not self.selected_files:
            messagebox.showerror("Error", "No files selected")
            return

        # Check if resilient transfers are enabled
        if self.use_resilient_transfers.get():
            self._send_files_resilient()
        else:
            self._send_files_standard()

    def _send_files_standard(self):
        """Send files using standard client."""
        if not self.client or not self.client.is_connected():
            messagebox.showerror("Error", "Not connected to server")
            return

        # Update transfer state
        self.transfer_active = True
        self.transfer_paused = False
        self._update_transfer_buttons_state('active')

        # Start transfer in separate thread
        self.transfer_thread = threading.Thread(target=self._transfer_files_thread, daemon=True)
        self.transfer_thread.start()

    def _send_files_resilient(self):
        """Send files using resilient client."""
        # Initialize resilient client if needed
        if not self.resilient_client:
            self.resilient_client = ResilientFileTransferClient()
            self._setup_resilient_client_callbacks()

        # Connect if not connected
        if not self._connect_to_server_resilient():
            return

        # Update transfer state
        self.transfer_active = True
        self.transfer_paused = False
        self._update_transfer_buttons_state('active')

        # Start resilient transfer in separate thread
        self.transfer_thread = threading.Thread(target=self._transfer_files_resilient_thread, daemon=True)
        self.transfer_thread.start()

    def _connect_to_server_resilient(self) -> bool:
        """Connect using resilient client."""
        try:
            host = self.host_var.get().strip()
            port = int(self.port_var.get().strip())

            if not host:
                messagebox.showerror("Error", "Please enter server address")
                return False

            if not (1 <= port <= 65535):
                messagebox.showerror("Error", "Port must be between 1 and 65535")
                return False

            self._update_transfer_status("Connecting...")

            if self.resilient_client.connect(host, port):
                self._update_transfer_status(f"Connected to {host}:{port}")
                return True
            else:
                self._update_transfer_status("Connection failed")
                messagebox.showerror("Connection Error", "Failed to connect to server")
                return False

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
            return False
        except Exception as e:
            self._update_transfer_status("Connection failed")
            messagebox.showerror("Connection Error", f"Error: {e}")
            return False

    def _transfer_files_resilient_thread(self):
        """Transfer files using resilient client in a separate thread."""
        try:
            total_files = len(self.selected_files)

            for i, file_path in enumerate(self.selected_files):
                # Check if transfer should be stopped
                if not self.transfer_active:
                    self.root.after(0, lambda: self._update_transfer_status("Transfer stopped"))
                    return

                # Check if transfer is paused
                while self.transfer_paused and self.transfer_active:
                    time.sleep(0.1)  # Wait while paused
                    if not self.transfer_active:  # Check if cancelled while paused
                        return

                # Update progress
                self.root.after(0, lambda i=i: self.send_progress_label.config(text=f"Sending {i+1}/{total_files}"))

                # Send file with resume capability
                success = self.resilient_client.send_file(file_path, resume=True)

                # Store current transfer ID for control operations
                if hasattr(self.resilient_client, 'current_transfer_id'):
                    self.current_transfer_id = self.resilient_client.current_transfer_id

                # Update file status in tree
                file_name = Path(file_path).name
                for item in self.file_tree.get_children():
                    if self.file_tree.item(item, 'text') == file_name:
                        status = "Sent" if success else "Failed"
                        self.file_tree.set(item, 'status', status)
                        break

                if not success:
                    self.root.after(0, lambda fn=file_name: messagebox.showerror("Transfer Error", f"Failed to send {fn}"))

            # Reset UI and transfer state
            self.transfer_active = False
            self.current_transfer_id = None
            self.root.after(0, lambda: self.send_progress.config(value=0))
            self.root.after(0, lambda: self.send_progress_label.config(text="Transfer complete"))
            self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

        except Exception as e:
            self.transfer_active = False
            self.current_transfer_id = None
            self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error during transfer: {e}"))
            self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

    def _transfer_files_thread(self):
        """Transfer files in a separate thread."""
        try:
            total_files = len(self.selected_files)

            for i, file_path in enumerate(self.selected_files):
                # Check if transfer should be stopped
                if not self.transfer_active:
                    self.root.after(0, lambda: self._update_transfer_status("Transfer stopped"))
                    return

                # Update progress
                self.root.after(0, lambda: self.send_progress_label.config(text=f"Sending {i+1}/{total_files}"))

                # Send file
                success = self.client.send_file(file_path)

                # Update file status in tree
                file_name = Path(file_path).name
                for item in self.file_tree.get_children():
                    if self.file_tree.item(item, 'text') == file_name:
                        status = "Sent" if success else "Failed"
                        self.file_tree.set(item, 'status', status)
                        break

                if not success:
                    self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Failed to send {file_name}"))

            # Reset UI and transfer state
            self.transfer_active = False
            self.current_transfer_id = None
            self.root.after(0, lambda: self.send_progress.config(value=0))
            self.root.after(0, lambda: self.send_progress_label.config(text="Transfer complete"))
            self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

        except Exception as e:
            self.transfer_active = False
            self.current_transfer_id = None
            self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error during transfer: {e}"))
            self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

    def _stop_transfer(self):
        """Stop all active transfers."""
        if self.use_resilient_transfers.get() and self.resilient_client:
            self.resilient_client.stop_all_transfers()
            self._update_transfer_buttons_state('stopped')
            self._update_transfer_status("Transfers stopped")
        else:
            # For standard transfers, we can't really stop mid-transfer
            # but we can prevent new transfers from starting
            self.transfer_active = False
            self._update_transfer_status("Transfer stopping...")

    def _pause_transfer(self):
        """Pause the current transfer."""
        if self.use_resilient_transfers.get() and self.resilient_client and self.current_transfer_id:
            self.resilient_client.pause_transfer(self.current_transfer_id)
            self.transfer_paused = True
            self._update_transfer_buttons_state('paused')
            self._update_transfer_status("Transfer paused")

    def _resume_transfer(self):
        """Resume the current transfer."""
        if self.use_resilient_transfers.get() and self.resilient_client:
            if self.current_transfer_id:
                self.resilient_client.resume_transfer(self.current_transfer_id)
            else:
                # Resume all transfers
                self.resilient_client.resume_all_transfers()
            self.transfer_paused = False
            self._update_transfer_buttons_state('active')
            self._update_transfer_status("Transfer resumed")

    def _cancel_transfer(self):
        """Cancel the current transfer completely."""
        if self.use_resilient_transfers.get() and self.resilient_client and self.current_transfer_id:
            result = messagebox.askyesno("Cancel Transfer",
                                       "Are you sure you want to cancel this transfer?\n"
                                       "This will permanently delete the transfer state.")
            if result:
                self.resilient_client.cancel_transfer(self.current_transfer_id)
                self.current_transfer_id = None
                self.transfer_active = False
                self.transfer_paused = False
                self._update_transfer_buttons_state('idle')
                self._update_transfer_status("Transfer cancelled")

    def _update_transfer_buttons_state(self, state: str):
        """Update the state of transfer control buttons."""
        # Check if we're connected
        connected = False
        if self.use_resilient_transfers.get():
            connected = self.resilient_client and self.resilient_client.is_connected()
        else:
            connected = self.client and self.client.is_connected()

        if state == 'idle':
            # No transfer active
            self.send_btn.config(state='normal' if (self.selected_files and connected) else 'disabled')
            self.stop_btn.config(state='disabled')
            self.pause_btn.config(state='disabled')
            self.resume_btn.config(state='disabled')
            self.cancel_btn.config(state='disabled')
        elif state == 'active':
            # Transfer is running
            self.send_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.pause_btn.config(state='normal')
            self.resume_btn.config(state='disabled')
            self.cancel_btn.config(state='normal')
        elif state == 'paused':
            # Transfer is paused
            self.send_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.pause_btn.config(state='disabled')
            self.resume_btn.config(state='normal')
            self.cancel_btn.config(state='normal')
        elif state == 'stopped':
            # Transfer is stopped (can be resumed)
            self.send_btn.config(state='disabled')
            self.stop_btn.config(state='disabled')
            self.pause_btn.config(state='disabled')
            self.resume_btn.config(state='normal')
            self.cancel_btn.config(state='normal')

    def _update_transfer_status(self, message: str):
        """Update the transfer status label."""
        self.send_progress_label.config(text=message)

    # Callback setup methods
    def _setup_server_callbacks(self):
        """Setup callbacks for server events."""
        if self.server:
            self.server.on_client_connected = self._on_client_connected
            self.server.on_client_disconnected = self._on_client_disconnected
            self.server.on_file_received = self._on_file_received
            self.server.on_transfer_progress = self._on_server_transfer_progress

    def _setup_client_callbacks(self):
        """Setup callbacks for client events."""
        if self.client:
            self.client.on_connected = self._on_connected
            self.client.on_disconnected = self._on_disconnected
            self.client.on_transfer_progress = self._on_client_transfer_progress
            self.client.on_transfer_complete = self._on_transfer_complete
            self.client.on_transfer_error = self._on_transfer_error

    # Server event callbacks
    def _on_client_connected(self, client_id: str, client_address: tuple):
        """Handle client connection."""
        message = f"Client connected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_client_disconnected(self, client_id: str):
        """Handle client disconnection."""
        message = f"Client disconnected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_file_received(self, filename: str, client_id: str):
        """Handle file received."""
        message = f"File received: {filename} from {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_server_transfer_progress(self, filename: str, progress: float, bytes_received: int, total_bytes: int, client_id: str):
        """Handle server transfer progress."""
        message = f"Receiving {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)})"
        self.root.after(0, lambda: self._log_message(message, replace_last=True))

    # Client event callbacks
    def _on_connected(self, host: str, port: int):
        """Handle successful connection."""
        pass  # Already handled in _connect_to_server

    def _on_disconnected(self):
        """Handle disconnection."""
        self.root.after(0, lambda: self.connection_status.config(text="Disconnected"))
        self.root.after(0, lambda: self.connect_btn.config(text="Connect"))
        self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

    def _on_client_transfer_progress(self, filename: str, progress: float, bytes_sent: int, total_bytes: int):
        """Handle client transfer progress."""
        self.root.after(0, lambda: self.send_progress.config(value=progress))
        message = f"Sending {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_sent)}/{FileUtils.format_file_size(total_bytes)})"
        self.root.after(0, lambda: self.send_progress_label.config(text=message))

    def _on_transfer_complete(self, filename: str, success: bool):
        """Handle transfer completion."""
        if success:
            message = f"Transfer completed: {filename}"
        else:
            message = f"Transfer failed: {filename}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_transfer_error(self, filename: str, error: str):
        """Handle transfer error."""
        message = f"Transfer error for {filename}: {error}"
        self.root.after(0, lambda: self._log_message(message))

    # Utility methods
    def _log_message(self, message: str, replace_last: bool = False):
        """Add a message to the activity log."""
        self.log_text.config(state='normal')

        if replace_last:
            # Remove last line and add new one
            self.log_text.delete("end-2l", "end-1l")

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def _update_status(self, message: str):
        """Update the status bar."""
        self.status_label.config(text=message)

    def _clear_activity_log(self):
        """Clear the activity log."""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def _save_activity_log(self):
        """Save the activity log to a file."""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="Save Activity Log",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    content = self.log_text.get(1.0, tk.END)
                    f.write(content)
                messagebox.showinfo("Success", f"Activity log saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save log: {e}")

    def _update_server_status_ui(self, running: bool, port: int = None):
        """Update server status indicators in the UI."""
        if running and port:
            # Update status indicator
            self.server_status_indicator.config(foreground="green")
            self.server_status_text.config(text=f"Server: Running on port {port}")

            # Update server address info
            local_ip = NetworkUtils.get_local_ip()
            self.server_address_label.config(text=f"{local_ip}:{port}")

        else:
            # Update status indicator
            self.server_status_indicator.config(foreground="red")
            self.server_status_text.config(text="Server: Stopped")

            # Update server info
            self.server_address_label.config(text="Not running")
            self.connected_clients_label.config(text="0")
            self.clients_info_label.config(text="Clients: 0")

    # New methods for enhanced functionality
    def _open_download_folder(self):
        """Open the download folder in the system file explorer."""
        try:
            import os
            import subprocess
            import platform

            download_path = self.download_dir_var.get()
            if not os.path.exists(download_path):
                os.makedirs(download_path, exist_ok=True)

            system = platform.system()
            if system == "Windows":
                os.startfile(download_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", download_path])
            else:  # Linux
                subprocess.run(["xdg-open", download_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open download folder: {e}")

    def _refresh_activity_log(self):
        """Refresh the activity log display."""
        # This could be enhanced to reload from a log file or update from server
        self._log_message("Activity log refreshed")

    def _browse_default_download_dir(self):
        """Browse for default download directory."""
        directory = filedialog.askdirectory(
            title="Select default download directory",
            initialdir=self.default_download_dir_var.get()
        )
        if directory:
            self.default_download_dir_var.set(directory)

    def _clear_server_log(self):
        """Clear the server log."""
        if hasattr(self, 'server_log_text'):
            self.server_log_text.config(state='normal')
            self.server_log_text.delete(1.0, tk.END)
            self.server_log_text.config(state='disabled')

    def _save_server_log(self):
        """Save the server log to a file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Server Log",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename and hasattr(self, 'server_log_text'):
                with open(filename, 'w', encoding='utf-8') as f:
                    content = self.server_log_text.get(1.0, tk.END)
                    f.write(content)
                messagebox.showinfo("Success", f"Server log saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save server log: {e}")

    def _export_server_stats(self):
        """Export server statistics."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Server Statistics",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                import json
                stats = {
                    "server_running": self.server is not None,
                    "connected_clients": 0,  # This would be updated with real data
                    "total_transfers": 0,    # This would be tracked
                    "total_bytes_transferred": 0,  # This would be tracked
                    "export_time": str(time.time())
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(stats, f, indent=2)
                messagebox.showinfo("Success", f"Statistics exported to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export statistics: {e}")

    def _reset_settings_to_defaults(self):
        """Reset all settings to default values."""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            # Reset network settings
            self.chunk_size_var.set("8")
            self.timeout_var.set("10")
            self.max_connections_var.set("5")

            # Reset UI settings
            self.font_size_var.set("9")
            self.theme_var.set("Modern")
            self.show_notifications_var.set(True)
            self.auto_clear_log_var.set(False)
            self.remember_window_size_var.set(True)

            # Reset transfer settings
            self.default_download_dir_var.set(str(Path.home() / "Downloads" / "FileTransfer"))
            self.overwrite_files_var.set(False)
            self.preserve_timestamps_var.set(True)
            self.create_subdirs_var.set(True)
            self.enable_resilient_default_var.set(True)
            self.auto_resume_var.set(False)

            # Reset security settings
            self.encryption_var.set(False)
            self.checksum_var.set(True)
            self.enable_compression_var.set(True)
            self.compression_level_var.set("6")

            # Reset advanced settings
            self.enable_logging_var.set(True)
            self.auto_save_logs_var.set(False)
            self.enable_statistics_var.set(True)
            self.auto_optimize_var.set(False)

            messagebox.showinfo("Settings Reset", "All settings have been reset to defaults.")

    def _export_settings(self):
        """Export current settings to a file."""
        try:
            filename = filedialog.asksaveasfilename(
                title="Export Settings",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                import json
                settings = {
                    "network": {
                        "chunk_size": self.chunk_size_var.get(),
                        "timeout": self.timeout_var.get(),
                        "max_connections": self.max_connections_var.get()
                    },
                    "ui": {
                        "font_size": self.font_size_var.get(),
                        "theme": self.theme_var.get(),
                        "show_notifications": self.show_notifications_var.get(),
                        "auto_clear_log": self.auto_clear_log_var.get(),
                        "remember_window_size": self.remember_window_size_var.get()
                    },
                    "transfer": {
                        "default_download_dir": self.default_download_dir_var.get(),
                        "overwrite_files": self.overwrite_files_var.get(),
                        "preserve_timestamps": self.preserve_timestamps_var.get(),
                        "create_subdirs": self.create_subdirs_var.get()
                    },
                    "security": {
                        "encryption": self.encryption_var.get(),
                        "checksum": self.checksum_var.get(),
                        "enable_compression": self.enable_compression_var.get(),
                        "compression_level": self.compression_level_var.get()
                    },
                    "advanced": {
                        "enable_logging": self.enable_logging_var.get(),
                        "auto_save_logs": self.auto_save_logs_var.get(),
                        "enable_statistics": self.enable_statistics_var.get(),
                        "auto_optimize": self.auto_optimize_var.get()
                    }
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2)
                messagebox.showinfo("Success", f"Settings exported to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export settings: {e}")

    def _import_settings(self):
        """Import settings from a file."""
        try:
            filename = filedialog.askopenfilename(
                title="Import Settings",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                import json
                with open(filename, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # Apply network settings
                if "network" in settings:
                    net = settings["network"]
                    self.chunk_size_var.set(net.get("chunk_size", "8"))
                    self.timeout_var.set(net.get("timeout", "10"))
                    self.max_connections_var.set(net.get("max_connections", "5"))

                # Apply UI settings
                if "ui" in settings:
                    ui = settings["ui"]
                    self.font_size_var.set(ui.get("font_size", "9"))
                    self.theme_var.set(ui.get("theme", "Modern"))
                    self.show_notifications_var.set(ui.get("show_notifications", True))
                    self.auto_clear_log_var.set(ui.get("auto_clear_log", False))
                    self.remember_window_size_var.set(ui.get("remember_window_size", True))

                # Apply other settings similarly...
                messagebox.showinfo("Success", f"Settings imported from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import settings: {e}")

    def _log_server_message(self, message: str):
        """Add a message to the server log."""
        if hasattr(self, 'server_log_text'):
            self.server_log_text.config(state='normal')
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            self.server_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.server_log_text.see(tk.END)
            self.server_log_text.config(state='disabled')

    def _show_resume_dialog(self):
        """Show dialog for resuming interrupted transfers."""
        if not self.use_resilient_transfers.get():
            messagebox.showinfo("Resume Transfers", "Resilient transfers must be enabled to use resume functionality.")
            return

        # Initialize resilient client if not already done
        if not self.resilient_client:
            self.resilient_client = ResilientFileTransferClient()
            self._setup_resilient_client_callbacks()

        # Get active transfers
        active_transfers = self.resilient_client.get_active_transfers()

        if not active_transfers:
            messagebox.showinfo("Resume Transfers", "No interrupted transfers found.")
            return

        # Create resume dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Resume Interrupted Transfers")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create transfer list
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Interrupted Transfers:", font=('Segoe UI', 12, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        # Transfer list with scrollbar
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        transfer_tree = ttk.Treeview(list_frame, columns=('size', 'progress', 'status'), show='tree headings', height=10)
        transfer_tree.heading('#0', text='File Name')
        transfer_tree.heading('size', text='Size')
        transfer_tree.heading('progress', text='Progress')
        transfer_tree.heading('status', text='Status')

        transfer_tree.column('#0', width=250)
        transfer_tree.column('size', width=100)
        transfer_tree.column('progress', width=100)
        transfer_tree.column('status', width=100)

        transfer_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=transfer_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        transfer_tree.configure(yscrollcommand=scrollbar.set)

        # Populate transfer list
        for transfer in active_transfers:
            size_str = FileUtils.format_file_size(transfer['file_size'])
            progress_str = f"{transfer['progress']:.1f}%"
            status_str = "Paused" if transfer['paused'] else "Ready"

            transfer_tree.insert('', 'end',
                               text=transfer['file_name'],
                               values=(size_str, progress_str, status_str),
                               tags=(transfer['transfer_id'],))

        # Buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)

        def resume_selected():
            selection = transfer_tree.selection()
            if not selection:
                messagebox.showwarning("Resume Transfer", "Please select a transfer to resume.")
                return

            item = transfer_tree.item(selection[0])
            transfer_id = item['tags'][0]

            # Connect if not connected
            if not self._connect_to_server():
                return

            # Resume transfer
            if self.resilient_client.resume_transfer(transfer_id):
                self._update_transfer_status("Resuming transfer...")
                dialog.destroy()
            else:
                messagebox.showerror("Resume Transfer", "Failed to resume transfer.")

        def cleanup_completed():
            count = self.resilient_client.cleanup_completed_transfers()
            messagebox.showinfo("Cleanup", f"Cleaned up {count} completed transfers.")
            dialog.destroy()

        ttk.Button(btn_frame, text="Resume Selected", command=resume_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="Cleanup Completed", command=cleanup_completed).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="Close", command=dialog.destroy).pack(side=tk.RIGHT)

    def _setup_resilient_client_callbacks(self):
        """Setup callbacks for resilient client."""
        if self.resilient_client:
            self.resilient_client.on_connected = self._on_resilient_connected
            self.resilient_client.on_disconnected = self._on_resilient_disconnected
            self.resilient_client.on_transfer_progress = self._on_resilient_transfer_progress
            self.resilient_client.on_transfer_complete = self._on_resilient_transfer_complete
            self.resilient_client.on_transfer_error = self._on_resilient_transfer_error
            self.resilient_client.on_transfer_paused = self._on_resilient_transfer_paused
            self.resilient_client.on_transfer_resumed = self._on_resilient_transfer_resumed
            self.resilient_client.on_transfer_cancelled = self._on_resilient_transfer_cancelled
            self.resilient_client.on_transfer_stopped = self._on_resilient_transfer_stopped

    def _on_resilient_connected(self, host: str, port: int):
        """Handle resilient client connection."""
        self.root.after(0, lambda: self._update_transfer_status(f"Connected to {host}:{port}"))

    def _on_resilient_disconnected(self):
        """Handle resilient client disconnection."""
        self.root.after(0, lambda: self._update_transfer_status("Disconnected"))

    def _on_resilient_transfer_progress(self, filename: str, progress: float, bytes_sent: int, total_bytes: int, speed: float = 0.0):
        """Handle resilient transfer progress."""
        speed_str = FileUtils.format_transfer_speed(speed)
        bytes_remaining = total_bytes - bytes_sent
        eta_str = FileUtils.estimate_time_remaining(bytes_remaining, speed)

        message = f"Sending {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_sent)}/{FileUtils.format_file_size(total_bytes)}) - {speed_str}"
        if eta_str != "Unknown":
            message += f" - ETA: {eta_str}"

        self.root.after(0, lambda: self._update_transfer_status(message))
        self.root.after(0, lambda: self._update_resilient_progress(progress))
        self.root.after(0, lambda: self._update_speed_display(speed, eta_str))

    def _on_resilient_transfer_complete(self, filename: str, success: bool):
        """Handle resilient transfer completion."""
        if success:
            message = f"Transfer completed: {filename}"
        else:
            message = f"Transfer failed: {filename}"
        self.root.after(0, lambda: self._update_transfer_status(message))
        self.root.after(0, lambda: self._update_resilient_progress(0))  # Hide progress bar

    def _on_resilient_transfer_error(self, filename: str, error: str):
        """Handle resilient transfer error."""
        message = f"Transfer error for {filename}: {error}"
        self.root.after(0, lambda: self._update_transfer_status(message))

    def _on_resilient_transfer_paused(self, transfer_id: str):
        """Handle resilient transfer pause."""
        self.transfer_paused = True
        self.root.after(0, lambda: self._update_transfer_status(f"Transfer paused: {transfer_id}"))
        self.root.after(0, lambda: self._update_transfer_buttons_state('paused'))

    def _on_resilient_transfer_resumed(self, transfer_id: str):
        """Handle resilient transfer resume."""
        self.transfer_paused = False
        self.root.after(0, lambda: self._update_transfer_status(f"Transfer resumed: {transfer_id}"))
        self.root.after(0, lambda: self._update_transfer_buttons_state('active'))

    def _on_resilient_transfer_cancelled(self, transfer_id: str):
        """Handle resilient transfer cancellation."""
        self.transfer_active = False
        self.transfer_paused = False
        self.current_transfer_id = None
        self.root.after(0, lambda: self._update_transfer_status(f"Transfer cancelled: {transfer_id}"))
        self.root.after(0, lambda: self._update_transfer_buttons_state('idle'))

    def _on_resilient_transfer_stopped(self):
        """Handle resilient transfer stop."""
        self.transfer_paused = True
        self.root.after(0, lambda: self._update_transfer_status("All transfers stopped"))
        self.root.after(0, lambda: self._update_transfer_buttons_state('stopped'))

    def _update_transfer_status(self, message: str):
        """Update transfer status display."""
        if hasattr(self, 'transfer_status_label'):
            self.transfer_status_label.config(text=message)

    def _update_resilient_progress(self, progress: float):
        """Update resilient transfer progress bar."""
        if hasattr(self, 'resilient_progress'):
            if progress > 0:
                self.resilient_progress.grid()  # Show progress bar
                self.resilient_progress['value'] = progress
            else:
                self.resilient_progress.grid_remove()  # Hide when no progress
                self._update_speed_display(0.0)  # Clear speed display

    def _update_speed_display(self, speed: float, eta: str = ""):
        """Update transfer speed display."""
        if hasattr(self, 'speed_label'):
            if speed > 0:
                speed_str = FileUtils.format_transfer_speed(speed)
                text = f"Speed: {speed_str}"
                if eta and eta != "Unknown":
                    text += f" | ETA: {eta}"
                self.speed_label.config(text=text)
            else:
                self.speed_label.config(text="")

    def run(self):
        """Start the GUI application."""
        self.logger.info("Starting GUI application")
        self._log_message("File Transfer Application started")
        self.root.mainloop()


if __name__ == "__main__":
    app = MainWindow()
    app.run()
