"""
File handling utilities.
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any
from src.utils.logger import get_logger


class FileUtils:
    """
    Utility class for file operations.
    """
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        Get file size in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File size in bytes, 0 if file doesn't exist
        """
        try:
            return Path(file_path).stat().st_size
        except (OSError, FileNotFoundError):
            return 0
    
    @staticmethod
    def get_file_info(file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive file information.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information or None if file doesn't exist
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return None
            
            stat = path.stat()
            
            return {
                "name": path.name,
                "size": stat.st_size,
                "size_human": FileUtils.format_file_size(stat.st_size),
                "modified": stat.st_mtime,
                "created": stat.st_ctime,
                "is_file": path.is_file(),
                "is_directory": path.is_dir(),
                "extension": path.suffix.lower(),
                "absolute_path": str(path.absolute())
            }
            
        except Exception as e:
            get_logger().error(f"Error getting file info for {file_path}: {e}")
            return None
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string (e.g., "1.5 MB")
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"

    @staticmethod
    def format_transfer_speed(bytes_per_second: float) -> str:
        """
        Format transfer speed in human-readable format.

        Args:
            bytes_per_second: Speed in bytes per second

        Returns:
            Formatted speed string (e.g., "1.5 MB/s")
        """
        if bytes_per_second == 0:
            return "0 B/s"

        size_names = ["B/s", "KB/s", "MB/s", "GB/s", "TB/s", "PB/s"]
        i = 0

        while bytes_per_second >= 1024 and i < len(size_names) - 1:
            bytes_per_second /= 1024.0
            i += 1

        return f"{bytes_per_second:.1f} {size_names[i]}"

    @staticmethod
    def estimate_time_remaining(bytes_remaining: int, bytes_per_second: float) -> str:
        """
        Estimate time remaining for transfer.

        Args:
            bytes_remaining: Bytes left to transfer
            bytes_per_second: Current transfer speed

        Returns:
            Formatted time string (e.g., "2m 30s")
        """
        if bytes_per_second <= 0:
            return "Unknown"

        seconds_remaining = bytes_remaining / bytes_per_second

        if seconds_remaining < 60:
            return f"{int(seconds_remaining)}s"
        elif seconds_remaining < 3600:
            minutes = int(seconds_remaining // 60)
            seconds = int(seconds_remaining % 60)
            return f"{minutes}m {seconds}s"
        else:
            hours = int(seconds_remaining // 3600)
            minutes = int((seconds_remaining % 3600) // 60)
            return f"{hours}h {minutes}m"

    @staticmethod
    def calculate_md5(file_path: str) -> str:
        """
        Calculate MD5 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            MD5 hash as hexadecimal string
        """
        hash_md5 = hashlib.md5()
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            
            return hash_md5.hexdigest()
            
        except Exception as e:
            get_logger().error(f"Error calculating MD5 for {file_path}: {e}")
            return ""
    
    @staticmethod
    def calculate_sha256(file_path: str) -> str:
        """
        Calculate SHA-256 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA-256 hash as hexadecimal string
        """
        hash_sha256 = hashlib.sha256()
        
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            
            return hash_sha256.hexdigest()
            
        except Exception as e:
            get_logger().error(f"Error calculating SHA-256 for {file_path}: {e}")
            return ""
    
    @staticmethod
    def is_safe_filename(filename: str) -> bool:
        """
        Check if filename is safe (no path traversal attempts).
        
        Args:
            filename: Filename to check
            
        Returns:
            True if filename is safe, False otherwise
        """
        # Check for path traversal attempts
        if ".." in filename or "/" in filename or "\\" in filename:
            return False
        
        # Check for reserved names on Windows
        reserved_names = {
            "CON", "PRN", "AUX", "NUL",
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
            "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
        }
        
        name_without_ext = Path(filename).stem.upper()
        if name_without_ext in reserved_names:
            return False
        
        # Check for invalid characters
        invalid_chars = '<>:"|?*'
        if any(char in filename for char in invalid_chars):
            return False
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename by removing/replacing invalid characters.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        # Remove path components
        filename = os.path.basename(filename)

        # Replace invalid characters (but keep dots for extensions)
        invalid_chars = '<>:"|?*\\/'
        for char in invalid_chars:
            filename = filename.replace(char, "_")

        # Remove leading/trailing spaces and dots
        filename = filename.strip(" ")

        # Ensure filename is not empty
        if not filename:
            filename = "unnamed_file"

        # Limit length
        if len(filename) > 255:
            name_part = filename[:200]
            ext_part = filename[-50:] if "." in filename[-50:] else ""
            filename = name_part + ext_part

        return filename
    
    @staticmethod
    def create_unique_filename(directory: str, filename: str) -> str:
        """
        Create a unique filename in the given directory.
        
        Args:
            directory: Target directory
            filename: Desired filename
            
        Returns:
            Unique filename that doesn't exist in the directory
        """
        base_path = Path(directory) / filename
        
        if not base_path.exists():
            return filename
        
        # Split filename and extension
        name_part = base_path.stem
        extension = base_path.suffix
        counter = 1
        
        while True:
            new_filename = f"{name_part}_{counter}{extension}"
            new_path = Path(directory) / new_filename
            
            if not new_path.exists():
                return new_filename
            
            counter += 1
    
    @staticmethod
    def ensure_directory_exists(directory: str) -> bool:
        """
        Ensure directory exists, create if it doesn't.
        
        Args:
            directory: Directory path
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            get_logger().error(f"Error creating directory {directory}: {e}")
            return False
    
    @staticmethod
    def get_available_space(directory: str) -> int:
        """
        Get available disk space in bytes.
        
        Args:
            directory: Directory path to check
            
        Returns:
            Available space in bytes, 0 if unable to determine
        """
        try:
            return shutil.disk_usage(directory).free
        except Exception as e:
            get_logger().error(f"Error getting disk space for {directory}: {e}")
            return 0
    
    @staticmethod
    def is_file_accessible(file_path: str) -> bool:
        """
        Check if file is accessible for reading.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is accessible, False otherwise
        """
        try:
            with open(file_path, 'rb') as f:
                f.read(1)  # Try to read one byte
            return True
        except Exception:
            return False
