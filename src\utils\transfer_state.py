"""
Transfer state management for interrupt-resilient file transfers.
"""

import json
import hashlib
import time
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from src.utils.logger import get_logger


@dataclass
class SegmentInfo:
    """Information about a downloaded segment."""
    start_byte: int
    end_byte: int
    checksum: str
    timestamp: float
    verified: bool = False


@dataclass
class TransferState:
    """Complete transfer state information."""
    file_name: str
    file_size: int
    total_checksum: str
    download_path: str
    segments: List[SegmentInfo]
    created_timestamp: float
    last_updated: float
    completed: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'file_name': self.file_name,
            'file_size': self.file_size,
            'total_checksum': self.total_checksum,
            'download_path': self.download_path,
            'segments': [asdict(segment) for segment in self.segments],
            'created_timestamp': self.created_timestamp,
            'last_updated': self.last_updated,
            'completed': self.completed
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TransferState':
        """Create from dictionary loaded from JSON."""
        segments = [SegmentInfo(**segment_data) for segment_data in data['segments']]
        return cls(
            file_name=data['file_name'],
            file_size=data['file_size'],
            total_checksum=data['total_checksum'],
            download_path=data['download_path'],
            segments=segments,
            created_timestamp=data['created_timestamp'],
            last_updated=data['last_updated'],
            completed=data.get('completed', False)
        )


class TransferStateManager:
    """Manages transfer state persistence and recovery."""
    
    def __init__(self, state_dir: Optional[str] = None):
        """
        Initialize the transfer state manager.
        
        Args:
            state_dir: Directory to store state files
        """
        self.logger = get_logger()
        
        if state_dir is None:
            self.state_dir = Path.home() / ".filetransfer" / "resume"
        else:
            self.state_dir = Path(state_dir)
        
        # Create state directory if it doesn't exist
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        # Active transfers
        self.active_transfers: Dict[str, TransferState] = {}
    
    def create_transfer_state(
        self,
        file_name: str,
        file_size: int,
        total_checksum: str,
        download_path: str
    ) -> str:
        """
        Create a new transfer state.
        
        Args:
            file_name: Name of the file being transferred
            file_size: Total size of the file
            total_checksum: Checksum of the complete file
            download_path: Path where file will be saved
            
        Returns:
            Transfer ID for tracking
        """
        transfer_id = self._generate_transfer_id(file_name, file_size)
        
        state = TransferState(
            file_name=file_name,
            file_size=file_size,
            total_checksum=total_checksum,
            download_path=download_path,
            segments=[],
            created_timestamp=time.time(),
            last_updated=time.time()
        )
        
        self.active_transfers[transfer_id] = state
        self._save_state(transfer_id, state)
        
        self.logger.info(f"Created transfer state for {file_name} (ID: {transfer_id})")
        return transfer_id
    
    def add_segment(
        self,
        transfer_id: str,
        start_byte: int,
        end_byte: int,
        data: bytes
    ) -> bool:
        """
        Add a completed segment to the transfer state.
        
        Args:
            transfer_id: Transfer ID
            start_byte: Starting byte position
            end_byte: Ending byte position
            data: Segment data for checksum calculation
            
        Returns:
            True if segment added successfully
        """
        if transfer_id not in self.active_transfers:
            self.logger.error(f"Transfer ID not found: {transfer_id}")
            return False
        
        state = self.active_transfers[transfer_id]
        
        # Calculate segment checksum
        segment_checksum = hashlib.md5(data).hexdigest()
        
        # Create segment info
        segment = SegmentInfo(
            start_byte=start_byte,
            end_byte=end_byte,
            checksum=segment_checksum,
            timestamp=time.time(),
            verified=True
        )
        
        # Add segment to state
        state.segments.append(segment)
        state.last_updated = time.time()
        
        # Save updated state
        self._save_state(transfer_id, state)
        
        self.logger.debug(f"Added segment {start_byte}-{end_byte} to transfer {transfer_id}")
        return True
    
    def get_missing_ranges(self, transfer_id: str, chunk_size: int = 8192) -> List[Tuple[int, int]]:
        """
        Get list of missing byte ranges for a transfer.
        
        Args:
            transfer_id: Transfer ID
            chunk_size: Size of chunks to create ranges for
            
        Returns:
            List of (start_byte, end_byte) tuples for missing ranges
        """
        if transfer_id not in self.active_transfers:
            return []
        
        state = self.active_transfers[transfer_id]
        
        # Sort segments by start byte
        segments = sorted(state.segments, key=lambda s: s.start_byte)
        
        missing_ranges = []
        current_pos = 0
        
        for segment in segments:
            if current_pos < segment.start_byte:
                # Gap found - add missing range(s)
                gap_start = current_pos
                gap_end = segment.start_byte - 1
                
                # Split gap into chunk-sized ranges
                while gap_start <= gap_end:
                    range_end = min(gap_start + chunk_size - 1, gap_end)
                    missing_ranges.append((gap_start, range_end))
                    gap_start = range_end + 1
            
            current_pos = max(current_pos, segment.end_byte + 1)
        
        # Check if there's a gap at the end
        if current_pos < state.file_size:
            gap_start = current_pos
            gap_end = state.file_size - 1
            
            # Split remaining gap into chunk-sized ranges
            while gap_start <= gap_end:
                range_end = min(gap_start + chunk_size - 1, gap_end)
                missing_ranges.append((gap_start, range_end))
                gap_start = range_end + 1
        
        return missing_ranges
    
    def get_progress(self, transfer_id: str) -> float:
        """
        Get transfer progress as percentage.
        
        Args:
            transfer_id: Transfer ID
            
        Returns:
            Progress percentage (0.0 to 100.0)
        """
        if transfer_id not in self.active_transfers:
            return 0.0
        
        state = self.active_transfers[transfer_id]
        
        if state.file_size == 0:
            return 100.0
        
        downloaded_bytes = sum(
            segment.end_byte - segment.start_byte + 1
            for segment in state.segments
        )
        
        return (downloaded_bytes / state.file_size) * 100.0
    
    def mark_completed(self, transfer_id: str) -> bool:
        """
        Mark a transfer as completed.
        
        Args:
            transfer_id: Transfer ID
            
        Returns:
            True if marked successfully
        """
        if transfer_id not in self.active_transfers:
            return False
        
        state = self.active_transfers[transfer_id]
        state.completed = True
        state.last_updated = time.time()
        
        self._save_state(transfer_id, state)
        self.logger.info(f"Transfer {transfer_id} marked as completed")
        return True
    
    def cleanup_transfer(self, transfer_id: str) -> bool:
        """
        Clean up transfer state files after successful completion.
        
        Args:
            transfer_id: Transfer ID
            
        Returns:
            True if cleaned up successfully
        """
        try:
            # Remove from active transfers
            if transfer_id in self.active_transfers:
                del self.active_transfers[transfer_id]
            
            # Remove state file
            state_file = self.state_dir / f"{transfer_id}.json"
            if state_file.exists():
                state_file.unlink()
                self.logger.info(f"Cleaned up transfer state for {transfer_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error cleaning up transfer {transfer_id}: {e}")
            return False
    
    def load_existing_transfers(self) -> List[str]:
        """
        Load existing transfer states from disk.
        
        Returns:
            List of transfer IDs that were loaded
        """
        loaded_transfers = []
        
        try:
            for state_file in self.state_dir.glob("*.json"):
                transfer_id = state_file.stem
                
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    state = TransferState.from_dict(data)
                    self.active_transfers[transfer_id] = state
                    loaded_transfers.append(transfer_id)
                    
                    self.logger.info(f"Loaded transfer state: {transfer_id}")
                    
                except Exception as e:
                    self.logger.error(f"Error loading state file {state_file}: {e}")
                    # Remove corrupted state file
                    try:
                        state_file.unlink()
                    except:
                        pass
        
        except Exception as e:
            self.logger.error(f"Error loading existing transfers: {e}")
        
        return loaded_transfers
    
    def _generate_transfer_id(self, file_name: str, file_size: int) -> str:
        """Generate a unique transfer ID."""
        data = f"{file_name}_{file_size}_{time.time()}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def _save_state(self, transfer_id: str, state: TransferState) -> bool:
        """Save transfer state to disk."""
        try:
            state_file = self.state_dir / f"{transfer_id}.json"
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state.to_dict(), f, indent=2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving transfer state {transfer_id}: {e}")
            return False
