"""
Interrupt-resilient file transfer client with resume capabilities.
"""

import socket
import json
import hashlib
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any, List, Tuple
from src.utils.logger import get_logger
from src.utils.transfer_state import TransferStateManager, TransferState
from src.utils.network_monitor import NetworkMonitor, ConnectionManager, ConnectionState


class ResilientFileTransferClient:
    """
    Enhanced file transfer client with interrupt resilience and resume capabilities.
    """
    
    def __init__(
        self,
        chunk_size: int = 65536,  # Increased from 8192 to 64KB for better performance
        state_dir: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 2.0
    ):
        """
        Initialize the resilient file transfer client.
        
        Args:
            chunk_size: Size of data chunks for transfer
            state_dir: Directory to store transfer state files
            max_retries: Maximum retry attempts for failed operations
            retry_delay: Delay between retry attempts
        """
        self.chunk_size = chunk_size
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.logger = get_logger()
        
        # Initialize components
        self.state_manager = TransferStateManager(state_dir)
        self.network_monitor = NetworkMonitor()
        self.connection_manager = ConnectionManager(self.network_monitor)
        
        # Load existing transfer states
        self.state_manager.load_existing_transfers()
        
        # Transfer state
        self.current_transfer_id: Optional[str] = None
        self.paused_transfers: Dict[str, bool] = {}
        self.cancelled_transfers: Dict[str, bool] = {}
        self.transfer_stop_flag: bool = False

        # Speed tracking
        self.transfer_start_time: Optional[float] = None
        self.last_speed_update: Optional[float] = None
        self.bytes_transferred_since_last_update: int = 0
        self.current_speed: float = 0.0  # bytes per second
        self.speed_history: List[float] = []  # for average speed calculation
        
        # Callbacks for GUI integration
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None  # (filename, progress, bytes_sent, total_bytes, speed)
        self.on_transfer_complete: Optional[Callable] = None
        self.on_transfer_error: Optional[Callable] = None
        self.on_transfer_paused: Optional[Callable] = None
        self.on_transfer_resumed: Optional[Callable] = None
        self.on_transfer_cancelled: Optional[Callable] = None
        self.on_transfer_stopped: Optional[Callable] = None
        
        # Setup network monitor callbacks
        self.network_monitor.on_disconnected = self._on_network_disconnected
        self.network_monitor.on_reconnected = self._on_network_reconnected
    
    def connect(self, host: str, port: int) -> bool:
        """
        Connect to the file transfer server.
        
        Args:
            host: Server host address
            port: Server port number
            
        Returns:
            True if connected successfully
        """
        success = self.connection_manager.connect(host, port)
        
        if success and self.on_connected:
            self.on_connected(host, port)
        
        return success
    
    def disconnect(self):
        """Disconnect from the server."""
        self.connection_manager.disconnect()
        
        if self.on_disconnected:
            self.on_disconnected()
    
    def send_file(self, file_path: str, resume: bool = True) -> bool:
        """
        Send a file to the connected server with resume capability.
        
        Args:
            file_path: Path to the file to send
            resume: Whether to attempt resuming if transfer state exists
            
        Returns:
            True if file sent successfully
        """
        file_path = Path(file_path)
        
        if not file_path.exists() or not file_path.is_file():
            self.logger.error(f"Invalid file path: {file_path}")
            return False
        
        try:
            # Calculate file metadata
            file_size = file_path.stat().st_size
            file_checksum = self._calculate_file_checksum(file_path)
            
            # Check for existing transfer state
            transfer_id = None
            if resume:
                transfer_id = self._find_existing_transfer(file_path.name, file_size, file_checksum)
            
            if transfer_id:
                self.logger.info(f"Resuming transfer for {file_path.name}")
                return self._resume_file_transfer(transfer_id, file_path)
            else:
                self.logger.info(f"Starting new transfer for {file_path.name}")
                return self._start_new_transfer(file_path, file_size, file_checksum)
                
        except Exception as e:
            self.logger.error(f"Error sending file {file_path.name}: {e}")
            if self.on_transfer_error:
                self.on_transfer_error(file_path.name, str(e))
            return False
    
    def pause_transfer(self, transfer_id: Optional[str] = None) -> bool:
        """
        Pause an active transfer.
        
        Args:
            transfer_id: Transfer ID to pause (current transfer if None)
            
        Returns:
            True if paused successfully
        """
        if transfer_id is None:
            transfer_id = self.current_transfer_id
        
        if transfer_id:
            self.paused_transfers[transfer_id] = True
            self.logger.info(f"Paused transfer {transfer_id}")
            
            if self.on_transfer_paused:
                self.on_transfer_paused(transfer_id)
            
            return True
        
        return False
    
    def resume_transfer(self, transfer_id: str) -> bool:
        """
        Resume a paused transfer.
        
        Args:
            transfer_id: Transfer ID to resume
            
        Returns:
            True if resumed successfully
        """
        if transfer_id in self.paused_transfers:
            del self.paused_transfers[transfer_id]
            
            # Get transfer state
            if transfer_id in self.state_manager.active_transfers:
                state = self.state_manager.active_transfers[transfer_id]
                file_path = Path(state.download_path).parent / state.file_name
                
                self.logger.info(f"Resuming transfer {transfer_id}")
                
                if self.on_transfer_resumed:
                    self.on_transfer_resumed(transfer_id)
                
                return self._resume_file_transfer(transfer_id, file_path)
        
        return False

    def cancel_transfer(self, transfer_id: Optional[str] = None) -> bool:
        """
        Cancel an active transfer completely.

        Args:
            transfer_id: Transfer ID to cancel (current transfer if None)

        Returns:
            True if cancelled successfully
        """
        if transfer_id is None:
            transfer_id = self.current_transfer_id

        if transfer_id:
            # Mark as cancelled
            self.cancelled_transfers[transfer_id] = True

            # Remove from paused transfers if it was paused
            if transfer_id in self.paused_transfers:
                del self.paused_transfers[transfer_id]

            # Clean up transfer state
            self.state_manager.remove_transfer_state(transfer_id)

            self.logger.info(f"Cancelled transfer {transfer_id}")

            if self.on_transfer_cancelled:
                self.on_transfer_cancelled(transfer_id)

            # Clear current transfer if it's the one being cancelled
            if self.current_transfer_id == transfer_id:
                self.current_transfer_id = None

            return True

        return False

    def stop_all_transfers(self) -> bool:
        """
        Stop all active transfers (can be resumed later).

        Returns:
            True if stopped successfully
        """
        self.transfer_stop_flag = True

        # Pause all active transfers
        active_transfers = list(self.state_manager.active_transfers.keys())
        for transfer_id in active_transfers:
            if not self.state_manager.active_transfers[transfer_id].completed:
                self.paused_transfers[transfer_id] = True

        self.logger.info("Stopped all active transfers")

        if self.on_transfer_stopped:
            self.on_transfer_stopped()

        return True

    def resume_all_transfers(self) -> bool:
        """
        Resume all stopped transfers.

        Returns:
            True if resumed successfully
        """
        self.transfer_stop_flag = False

        # Clear paused state for all transfers
        self.paused_transfers.clear()

        self.logger.info("Resumed all transfers")

        return True

    def get_active_transfers(self) -> List[Dict[str, Any]]:
        """
        Get list of active transfers with their progress.
        
        Returns:
            List of transfer information dictionaries
        """
        transfers = []
        
        for transfer_id, state in self.state_manager.active_transfers.items():
            if not state.completed:
                progress = self.state_manager.get_progress(transfer_id)
                transfers.append({
                    'transfer_id': transfer_id,
                    'file_name': state.file_name,
                    'file_size': state.file_size,
                    'progress': progress,
                    'paused': transfer_id in self.paused_transfers,
                    'created': state.created_timestamp,
                    'last_updated': state.last_updated
                })
        
        return transfers
    
    def cleanup_completed_transfers(self) -> int:
        """
        Clean up completed transfer state files.
        
        Returns:
            Number of transfers cleaned up
        """
        cleaned_count = 0
        
        for transfer_id, state in list(self.state_manager.active_transfers.items()):
            if state.completed:
                if self.state_manager.cleanup_transfer(transfer_id):
                    cleaned_count += 1
        
        return cleaned_count
    
    def _start_new_transfer(self, file_path: Path, file_size: int, file_checksum: str) -> bool:
        """Start a new file transfer."""
        # Reset speed tracking for new transfer
        self._reset_speed_tracking()

        # Create transfer state
        transfer_id = self.state_manager.create_transfer_state(
            file_name=file_path.name,
            file_size=file_size,
            total_checksum=file_checksum,
            download_path=str(file_path.parent)
        )

        self.current_transfer_id = transfer_id
        
        # Send file metadata to server
        metadata = {
            "filename": file_path.name,
            "size": file_size,
            "checksum": file_checksum,
            "transfer_id": transfer_id,
            "resume": False
        }
        
        if not self._send_metadata(metadata):
            return False
        
        # Send file content in chunks
        return self._send_file_chunks(transfer_id, file_path, 0, file_size - 1)
    
    def _resume_file_transfer(self, transfer_id: str, file_path: Path) -> bool:
        """Resume an existing file transfer."""
        self.current_transfer_id = transfer_id
        state = self.state_manager.active_transfers[transfer_id]
        
        # Get missing ranges
        missing_ranges = self.state_manager.get_missing_ranges(transfer_id, self.chunk_size)
        
        if not missing_ranges:
            # Transfer is already complete
            self.state_manager.mark_completed(transfer_id)
            if self.on_transfer_complete:
                self.on_transfer_complete(file_path.name, True)
            return True
        
        # Send resume metadata to server
        metadata = {
            "filename": file_path.name,
            "size": state.file_size,
            "checksum": state.total_checksum,
            "transfer_id": transfer_id,
            "resume": True,
            "missing_ranges": missing_ranges
        }
        
        if not self._send_metadata(metadata):
            return False
        
        # Send missing chunks
        for start_byte, end_byte in missing_ranges:
            # Check if transfer is cancelled
            if transfer_id in self.cancelled_transfers:
                self.logger.info(f"Transfer {transfer_id} cancelled")
                return False

            # Check if all transfers are stopped
            if self.transfer_stop_flag:
                self.logger.info(f"Transfer {transfer_id} stopped")
                return True

            if transfer_id in self.paused_transfers:
                self.logger.info(f"Transfer {transfer_id} paused")
                return True

            if not self._send_file_chunks(transfer_id, file_path, start_byte, end_byte):
                return False
        
        # Mark as completed
        self.state_manager.mark_completed(transfer_id)
        
        if self.on_transfer_complete:
            self.on_transfer_complete(file_path.name, True)
        
        return True
    
    def _send_file_chunks(self, transfer_id: str, file_path: Path, start_byte: int, end_byte: int) -> bool:
        """Send file chunks for a specific byte range."""
        try:
            with open(file_path, 'rb') as f:
                f.seek(start_byte)
                current_pos = start_byte
                
                while current_pos <= end_byte:
                    # Check if transfer is cancelled
                    if transfer_id in self.cancelled_transfers:
                        self.logger.info(f"Transfer {transfer_id} cancelled")
                        return False

                    # Check if all transfers are stopped
                    if self.transfer_stop_flag:
                        self.logger.info(f"Transfer {transfer_id} stopped")
                        return True

                    # Check if transfer is paused
                    if transfer_id in self.paused_transfers:
                        return True
                    
                    # Calculate chunk size
                    remaining = end_byte - current_pos + 1
                    chunk_size = min(self.chunk_size, remaining)
                    
                    # Read chunk
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    
                    # Send chunk with retry
                    if not self.connection_manager.send_with_retry(chunk):
                        self.logger.error(f"Failed to send chunk at position {current_pos}")
                        return False

                    # Update speed tracking
                    self._update_speed_tracking(len(chunk))

                    # Update transfer state
                    chunk_end = current_pos + len(chunk) - 1
                    self.state_manager.add_segment(transfer_id, current_pos, chunk_end, chunk)

                    current_pos += len(chunk)

                    # Report progress with speed
                    if self.on_transfer_progress:
                        state = self.state_manager.active_transfers[transfer_id]
                        progress = self.state_manager.get_progress(transfer_id)
                        total_sent = sum(seg.end_byte - seg.start_byte + 1 for seg in state.segments)
                        current_speed = self.get_current_speed()
                        self.on_transfer_progress(file_path.name, progress, total_sent, state.file_size, current_speed)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending file chunks: {e}")
            return False
    
    def _send_metadata(self, metadata: Dict[str, Any]) -> bool:
        """Send file metadata to server."""
        try:
            metadata_data = json.dumps(metadata).encode('utf-8')
            metadata_length = len(metadata_data)
            
            # Send metadata length then metadata
            length_bytes = metadata_length.to_bytes(4, byteorder='big')
            
            if not self.connection_manager.send_with_retry(length_bytes):
                return False
            
            if not self.connection_manager.send_with_retry(metadata_data):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending metadata: {e}")
            return False
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate MD5 checksum of entire file."""
        hash_md5 = hashlib.md5()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def _find_existing_transfer(self, file_name: str, file_size: int, file_checksum: str) -> Optional[str]:
        """Find existing transfer state for a file."""
        for transfer_id, state in self.state_manager.active_transfers.items():
            if (state.file_name == file_name and 
                state.file_size == file_size and 
                state.total_checksum == file_checksum and 
                not state.completed):
                return transfer_id
        return None
    
    def _on_network_disconnected(self):
        """Handle network disconnection."""
        self.logger.warning("Network disconnected - pausing active transfers")
        
        if self.current_transfer_id:
            self.pause_transfer(self.current_transfer_id)
    
    def _on_network_reconnected(self):
        """Handle network reconnection."""
        self.logger.info("Network reconnected - transfers can be resumed")

        # Note: We don't automatically resume here to give user control

    def is_connected(self) -> bool:
        """Check if client is connected to server."""
        return self.connection_manager.connected

    def get_network_state(self) -> ConnectionState:
        """Get current network connection state."""
        return self.network_monitor.get_state()

    def _update_speed_tracking(self, bytes_sent: int):
        """Update transfer speed tracking."""
        current_time = time.time()

        if self.transfer_start_time is None:
            self.transfer_start_time = current_time
            self.last_speed_update = current_time
            self.bytes_transferred_since_last_update = 0
            return

        # Update bytes transferred
        self.bytes_transferred_since_last_update += bytes_sent

        # Calculate speed every second
        time_since_last_update = current_time - self.last_speed_update
        if time_since_last_update >= 1.0:  # Update every second
            # Calculate current speed (bytes per second)
            self.current_speed = self.bytes_transferred_since_last_update / time_since_last_update

            # Add to speed history for average calculation
            self.speed_history.append(self.current_speed)
            if len(self.speed_history) > 10:  # Keep last 10 seconds
                self.speed_history.pop(0)

            # Reset for next calculation
            self.bytes_transferred_since_last_update = 0
            self.last_speed_update = current_time

    def get_current_speed(self) -> float:
        """Get current transfer speed in bytes per second."""
        return self.current_speed

    def get_average_speed(self) -> float:
        """Get average transfer speed in bytes per second."""
        if not self.speed_history:
            return 0.0
        return sum(self.speed_history) / len(self.speed_history)

    def _reset_speed_tracking(self):
        """Reset speed tracking for new transfer."""
        self.transfer_start_time = None
        self.last_speed_update = None
        self.bytes_transferred_since_last_update = 0
        self.current_speed = 0.0
        self.speed_history.clear()
